/* Admin Dashboard Styles - Professional Business Theme */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    
    --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 14px;
    --line-height-base: 1.5;
    
    --border-radius: 6px;
    --border-radius-sm: 4px;
    --border-radius-lg: 8px;
    
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --box-shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --box-shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
    
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans-serif);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--gray-800);
    background-color: var(--gray-100);
}

/* Layout Structure */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

.admin-sidebar {
    width: 250px;
    background-color: var(--primary-color);
    color: var(--white);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: var(--transition);
}

.admin-sidebar.collapsed {
    width: 60px;
}

.admin-main {
    flex: 1;
    margin-left: 250px;
    transition: var(--transition);
}

.admin-main.expanded {
    margin-left: 60px;
}

/* Sidebar Styles */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-logo {
    font-size: 20px;
    font-weight: bold;
    color: var(--white);
    text-decoration: none;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-link:hover,
.nav-link.active {
    color: var(--white);
    background-color: rgba(255,255,255,0.1);
    border-left-color: var(--secondary-color);
}

.nav-link i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

.nav-text {
    transition: var(--transition);
}

.admin-sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Top Header */
.admin-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--box-shadow-sm);
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--gray-600);
    cursor: pointer;
    margin-right: 20px;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-dropdown {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.user-info:hover {
    background-color: var(--gray-200);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: bold;
    margin-right: 10px;
}

.user-name {
    font-weight: 500;
    color: var(--gray-800);
}

/* Main Content Area */
.admin-content {
    padding: 30px;
    min-height: calc(100vh - 80px);
}

/* Cards */
.card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.card-body {
    padding: 25px;
}

.card-footer {
    padding: 15px 25px;
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white);
    margin-right: 20px;
}

.stat-icon.primary { background-color: var(--primary-color); }
.stat-icon.secondary { background-color: var(--secondary-color); }
.stat-icon.success { background-color: var(--success-color); }
.stat-icon.warning { background-color: var(--warning-color); }
.stat-icon.danger { background-color: var(--danger-color); }

.stat-content h3 {
    font-size: 28px;
    font-weight: bold;
    color: var(--gray-800);
    margin: 0 0 5px 0;
}

.stat-content p {
    color: var(--gray-600);
    margin: 0;
    font-size: 14px;
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 15px;
    background-color: var(--white);
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    color: var(--gray-600);
    text-decoration: none;
    transition: var(--transition);
    min-height: 100px;
}

.action-btn:hover {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.05);
    text-decoration: none;
}

.action-btn i {
    font-size: 24px;
    margin-bottom: 8px;
}

.action-btn span {
    font-weight: 500;
    font-size: 14px;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.activity-item:hover {
    background-color: var(--gray-50);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.activity-info {
    flex: 1;
}

.activity-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-800);
}

.activity-info p {
    margin: 0;
    font-size: 13px;
    color: var(--gray-600);
}

.activity-time {
    font-size: 12px;
    color: var(--gray-500);
    white-space: nowrap;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.col-md-4 {
    flex: 0 0 33.333%;
    max-width: 33.333%;
    padding: 0 15px;
}

.col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: 0 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.show {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
    }

    .admin-content {
        padding: 20px 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 20px;
    }

    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
    }

    .activity-info {
        text-align: center;
    }
}
