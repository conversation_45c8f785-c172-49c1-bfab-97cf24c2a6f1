/* Professional Components for Admin Dashboard */

/* Card Components */
.card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    padding: 20px;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 20px;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

@media (max-width: 768px) {
    .col-md-6, .col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 15px;
    }
}

/* Utility Classes */
.text-danger {
    color: var(--danger-color) !important;
}

.text-muted {
    color: var(--gray-600) !important;
}

.bg-light {
    background-color: var(--gray-100) !important;
}

.rounded {
    border-radius: var(--border-radius) !important;
}

.shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

.d-flex {
    display: flex !important;
}

.align-items-center {
    align-items: center !important;
}

.justify-content-center {
    justify-content: center !important;
}

.mt-2 {
    margin-top: 10px !important;
}

.mb-4 {
    margin-bottom: 25px !important;
}

.fa-3x {
    font-size: 3em !important;
}

/* Alerts and Messages */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    position: relative;
    display: flex;
    align-items: center;
}

.alert-dismissible {
    padding-right: 50px;
}

.alert-close {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

.alert-close:hover {
    opacity: 1;
}

.alert-icon {
    margin-right: 12px;
    font-size: 18px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Flash Messages */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.flash-message {
    margin-bottom: 10px;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-out;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-dialog {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideInDown 0.3s ease-out;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--gray-400);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--gray-800);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 15px 25px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInDown {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Login Page Styles */
.login-page {
    background: var(--primary-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.login-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    overflow: hidden;
}

.login-header {
    text-align: center;
    padding: 40px 30px 30px;
    background-color: var(--gray-50);
}

.login-header .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 15px;
}

.login-header .logo i {
    font-size: 32px;
    color: var(--primary-color);
}

.login-header h1 {
    font-size: 24px;
    margin: 0;
    color: var(--gray-900);
    font-weight: 600;
}

.login-header .subtitle {
    color: var(--gray-600);
    font-size: 14px;
    margin: 5px 0 0 0;
}

.login-form {
    padding: 30px;
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: none;
    border: 1px solid var(--gray-300);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.dropdown-toggle:hover {
    background-color: var(--gray-100);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    min-width: 160px;
    padding: 8px 0;
    margin: 2px 0 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 8px 16px;
    clear: both;
    font-weight: 400;
    color: var(--gray-800);
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.dropdown-divider {
    height: 0;
    margin: 8px 0;
    overflow: hidden;
    border-top: 1px solid var(--gray-200);
}

/* Tabs */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: 25px;
}

.nav-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 2px solid transparent;
    font-weight: 500;
}

.nav-tab:hover {
    color: var(--primary-color);
}

.nav-tab.active {
    color: var(--secondary-color);
    border-bottom-color: var(--secondary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Progress Bar */
.progress {
    height: 8px;
    background-color: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-bar {
    height: 100%;
    background-color: var(--secondary-color);
    transition: width 0.3s ease;
}

.progress-bar.success {
    background-color: var(--success-color);
}

.progress-bar.warning {
    background-color: var(--warning-color);
}

.progress-bar.danger {
    background-color: var(--danger-color);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--gray-800);
    color: var(--white);
    text-align: center;
    border-radius: var(--border-radius-sm);
    padding: 8px 12px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--gray-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: 15px 0;
    margin-bottom: 20px;
    list-style: none;
    background-color: transparent;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    padding: 0 8px;
    color: var(--gray-400);
}

.breadcrumb-item a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--gray-600);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-content {
    text-align: center;
    color: var(--primary-color);
}

.loading-spinner-large {
    width: 48px;
    height: 48px;
    border: 4px solid var(--gray-200);
    border-top-color: var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--gray-500); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-light { background-color: var(--light-color); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.font-weight-bold { font-weight: bold; }
.font-weight-normal { font-weight: normal; }

.border { border: 1px solid var(--gray-200); }
.border-top { border-top: 1px solid var(--gray-200); }
.border-bottom { border-bottom: 1px solid var(--gray-200); }
.border-0 { border: 0; }

.rounded { border-radius: var(--border-radius); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }

.shadow { box-shadow: var(--box-shadow); }
.shadow-sm { box-shadow: var(--box-shadow-sm); }
.shadow-lg { box-shadow: var(--box-shadow-lg); }
