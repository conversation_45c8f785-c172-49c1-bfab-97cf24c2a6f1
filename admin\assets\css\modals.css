/* Modal Styles */

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
    animation: modalFadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: var(--white);
    margin: var(--spacing-4);
    padding: 0;
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease;
    position: relative;
}

@keyframes modalSlideIn {
    from { 
        transform: translateY(-50px); 
        opacity: 0; 
    }
    to { 
        transform: translateY(0); 
        opacity: 1; 
    }
}

.modal-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius);
    transition: var(--transition);
    line-height: 1;
}

.modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-6);
}

.modal-body p {
    margin-bottom: var(--spacing-4);
    color: var(--gray-600);
}

.modal-body .text-danger {
    color: var(--danger);
}

.modal-body small {
    font-size: var(--font-size-xs);
}

.modal-footer {
    padding: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-3);
}

/* Delete Confirmation Modal */
.delete-modal {
    animation: modalFadeIn 0.4s ease;
}

.delete-modal .modal-content {
    max-width: 480px;
    border: none;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.delete-modal .modal-header {
    background: linear-gradient(135deg, #fee2e2 0%, #fef2f2 100%);
    border-bottom: 1px solid #fecaca;
    padding: var(--spacing-8) var(--spacing-6) var(--spacing-6);
    position: relative;
    text-align: center;
    flex-direction: column;
    gap: var(--spacing-4);
}

.delete-modal .modal-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.delete-modal .modal-icon i {
    font-size: 28px;
    color: white;
}

.delete-modal .modal-title {
    color: #991b1b;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    text-align: center;
}

.delete-modal .modal-close {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #fecaca;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-modal .modal-close:hover {
    background: white;
    border-color: #f87171;
}

.delete-modal .modal-body {
    padding: var(--spacing-8) var(--spacing-6);
    text-align: center;
}

.delete-modal .delete-warning {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    border-radius: var(--radius-lg);
    padding: var(--spacing-5);
    margin-bottom: var(--spacing-4);
}

.delete-modal .warning-content h4 {
    color: #92400e;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 var(--spacing-3) 0;
}

.delete-modal .item-name {
    background: #fef3c7;
    border: 1px solid #fcd34d;
    border-radius: var(--radius);
    padding: var(--spacing-3) var(--spacing-4);
    margin: var(--spacing-5) 0;
    display: inline-block;
    font-size: 14px;
}

.delete-modal .item-name .delete-item-name {
    color: #92400e;
    font-weight: 600;
}

.delete-modal .warning-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-3);
    color: #b45309;
    font-size: 14px;
    margin-top: var(--spacing-6);
    padding: var(--spacing-4);
    background: rgba(251, 191, 36, 0.1);
    border-radius: var(--radius);
}

.delete-modal .warning-message i {
    color: #f59e0b;
}

.delete-modal .modal-footer {
    background: #fafafa;
    border-top: 1px solid #e5e7eb;
    padding: var(--spacing-6) var(--spacing-6);
    gap: var(--spacing-4);
}

.delete-modal .btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    padding: var(--spacing-3) var(--spacing-6);
    font-weight: 600;
    box-shadow: 0 4px 14px rgba(239, 68, 68, 0.3);
    transition: all 0.3s ease;
}

.delete-modal .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    transform: translateY(-1px);
}

.delete-modal .btn-secondary {
    background: white;
    border: 2px solid #d1d5db;
    color: #6b7280;
    padding: var(--spacing-3) var(--spacing-6);
    font-weight: 600;
    transition: all 0.3s ease;
}

.delete-modal .btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
    transform: translateY(-1px);
}

/* Form Modal */
.form-modal .modal-content {
    max-width: 600px;
}

.form-modal .modal-body {
    padding: var(--spacing-6);
}

.form-modal .form-group {
    margin-bottom: var(--spacing-4);
}

/* Image Upload Modal */
.image-upload-modal .modal-content {
    max-width: 800px;
}

.image-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--spacing-8);
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: var(--primary);
    background-color: rgba(99, 102, 241, 0.05);
}

.image-upload-area.dragover {
    border-color: var(--primary);
    background-color: rgba(99, 102, 241, 0.1);
}

.image-upload-area i {
    font-size: var(--font-size-3xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
}

.image-upload-area h4 {
    margin-bottom: var(--spacing-2);
    color: var(--gray-700);
}

.image-upload-area p {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    margin: 0;
}

.image-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-4);
    margin-top: var(--spacing-4);
}

.image-preview-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.image-preview-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.image-preview-item .remove-image {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    background-color: var(--danger);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: var(--transition);
}

.image-preview-item .remove-image:hover {
    background-color: #dc2626;
}

/* Progress Modal */
.progress-modal .modal-body {
    text-align: center;
    padding: var(--spacing-8);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin: var(--spacing-4) 0;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary);
    border-radius: var(--radius-lg);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-top: var(--spacing-2);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-6);
    right: var(--spacing-6);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.toast {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-4);
    min-width: 300px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    animation: toastSlideIn 0.3s ease;
    border-left: 4px solid var(--primary);
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toastSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast.success {
    border-left-color: var(--success);
}

.toast.warning {
    border-left-color: var(--warning);
}

.toast.error {
    border-left-color: var(--danger);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast.success .toast-icon {
    color: var(--success);
}

.toast.warning .toast-icon {
    color: var(--warning);
}

.toast.error .toast-icon {
    color: var(--danger);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-sm);
}

.toast-message {
    color: var(--gray-600);
    font-size: var(--font-size-xs);
    margin: 0;
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius);
    transition: var(--transition);
}

.toast-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

/* Responsive Modal Styles */
@media (max-width: 768px) {
    .modal-content {
        margin: var(--spacing-2);
        width: calc(100% - var(--spacing-4));
        max-height: calc(100vh - var(--spacing-4));
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-4);
    }
    
    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .modal-footer .btn {
        width: 100%;
    }
    
    .toast-container {
        top: var(--spacing-4);
        right: var(--spacing-4);
        left: var(--spacing-4);
    }
    
    .toast {
        min-width: auto;
        max-width: none;
    }
}
