/* Professional Form Styles for Admin Dashboard */

/* Form Container */
.form-container {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 30px;
}

.form-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--gray-200);
}

.form-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 5px 0;
}

.form-subtitle {
    color: var(--gray-600);
    margin: 0;
    font-size: 14px;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

.form-col-6 {
    flex: 0 0 50%;
}

.form-col-4 {
    flex: 0 0 33.333%;
}

.form-col-3 {
    flex: 0 0 25%;
}

/* Labels */
.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--gray-800);
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: var(--danger-color);
}

/* Input Fields */
.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    line-height: 1.5;
    color: var(--gray-800);
    background-color: var(--white);
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:disabled {
    background-color: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

/* Textarea */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

textarea.form-control.large {
    min-height: 200px;
}

/* Select Dropdown */
.form-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    color: var(--gray-800);
    background-color: var(--white);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px 12px;
    appearance: none;
    transition: var(--transition);
}

.form-select:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Checkbox and Radio */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-right: 10px;
    accent-color: var(--secondary-color);
}

.form-check-label {
    font-size: 14px;
    color: var(--gray-800);
    cursor: pointer;
}

.form-check-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

/* File Upload */
.file-upload-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    background-color: var(--gray-50);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.file-upload-label:hover {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.05);
}

.file-upload-icon {
    font-size: 32px;
    color: var(--gray-400);
    margin-bottom: 10px;
}

.file-upload-text {
    color: var(--gray-600);
    font-size: 14px;
}

.file-upload-text strong {
    color: var(--secondary-color);
}

/* Image Preview */
.image-preview {
    margin-top: 15px;
    display: none;
}

.image-preview.show {
    display: block;
}

.preview-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Current Image Display (for edit forms) */
.current-image-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
}

.current-image {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.current-image-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--box-shadow-sm);
    flex-shrink: 0;
}

.current-image-info {
    flex: 1;
    min-width: 0;
}

.current-image-label {
    display: block;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 14px;
}

.delete-image-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: var(--danger-color);
    margin-top: 10px;
}

.delete-image-checkbox input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
}

.delete-image-checkbox:hover {
    color: var(--danger-color-dark);
}

/* Form Validation */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 12px;
    color: var(--danger-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 12px;
    color: var(--success-color);
}

/* Form Actions */
.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.form-actions.center {
    justify-content: center;
}

.form-actions.left {
    justify-content: flex-start;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    min-width: 120px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    color: var(--white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--dark-color);
    border-color: var(--dark-color);
}

.btn-secondary {
    color: var(--white);
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    color: var(--white);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background-color: #229954;
    border-color: #229954;
}

.btn-warning {
    color: var(--white);
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover:not(:disabled) {
    background-color: #e67e22;
    border-color: #e67e22;
}

.btn-danger {
    color: var(--white);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #c0392b;
    border-color: #c0392b;
}

.btn-outline-primary {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
    color: var(--white);
    background-color: var(--primary-color);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    background-color: transparent;
    border-color: var(--secondary-color);
}

.btn-outline-secondary:hover:not(:disabled) {
    color: var(--white);
    background-color: var(--secondary-color);
}

/* Button Sizes */
.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
    min-width: 80px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    min-width: 160px;
}

/* Loading State */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Forms */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-col,
    .form-col-6,
    .form-col-4,
    .form-col-3 {
        flex: none;
        width: 100%;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}
