# Homestay Management System

A comprehensive web application for managing homestay bookings, rooms, activities, and services. Built with PHP and MySQL, featuring an admin dashboard for content management and a responsive frontend for guests.

## Features

- Room booking and management
- Activity and event scheduling
- Blog content management
- Car rental services
- Contact form handling
- Image gallery management
- Responsive design

## Technologies Used

- PHP
- MySQL
- HTML/CSS/JavaScript
- Bootstrap
- TinyMCE Editor
- Font Awesome Icons

## Development

This project was developed with assistance from GitHub Copilot for enhanced productivity and code quality.